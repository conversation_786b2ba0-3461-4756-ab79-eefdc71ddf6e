// Arquivo de rotas limpo - apenas Supabase
import express from 'express';
import { WhatsAppClient } from '../services/whatsappService';
import { GeminiAIService } from '../services/geminiService';

// Importar rotas modulares
import authRoutes from './auth';
import messageRoutes from './messages';
import analyticsRoutes from './analytics';
import whatsappRoutes, { setWhatsAppClient } from './whatsapp';
import scheduleRoutes from './schedules';
import templateRoutes from './templates';
import webhookRoutes from './webhooks';
import healthRoutes from './health';

// Importar rotas Supabase
import { setupSupabaseRoutes } from './supabaseRoutes';

export function setupRoutes(app: express.Application, whatsappClient: WhatsAppClient, geminiService: GeminiAIService) {
  // Configurar WhatsApp client nas rotas
  setWhatsAppClient(whatsappClient);

  // =====================================================
  // ROTAS SUPABASE (SUBSTITUI MONGODB)
  // =====================================================
  setupSupabaseRoutes(app);

  // =====================================================
  // ROTAS COM AUTENTICAÇÃO
  // =====================================================

  // Rotas de autenticação
  app.use('/api/auth', authRoutes);

  // Rotas de mensagens
  app.use('/api/messages', messageRoutes);

  // Rotas de analytics
  app.use('/api/analytics', analyticsRoutes);

  // Rotas do WhatsApp
  app.use('/api/whatsapp', whatsappRoutes);

  // Rotas de agendamentos proativos
  app.use('/api/schedules', scheduleRoutes);

  // Rotas de templates de mensagens
  app.use('/api/templates', templateRoutes);

  // Rotas de webhooks
  app.use('/api/webhooks', webhookRoutes);

  // Rotas de monitoramento de saúde
  app.use('/api/health', healthRoutes);

  // =====================================================
  // ROTAS DE UTILIDADE
  // =====================================================

  // Rota de health check básico
  app.get('/api/health', (req, res) => {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      database: 'Supabase',
      mongodb_removed: true
    });
  });

  // Rota para processar mensagem com IA
  app.post('/api/ai/process', async (req, res) => {
    try {
      const { message, contactId } = req.body;
      
      if (!contactId) {
        return res.status(400).json({ error: 'ID do contato é obrigatório' });
      }

      // Processar com IA usando Gemini
      const aiResponse = await geminiService.processMessage(message, contactId);
      
      res.json({
        originalMessage: message,
        aiResponse: aiResponse,
        contactId: contactId,
        timestamp: new Date().toISOString()
      });
    } catch (error: any) {
      console.error('❌ Erro ao processar mensagem com IA:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Rota para verificar status do sistema
  app.get('/api/system/status', (req, res) => {
    res.json({
      status: 'operational',
      database: 'Supabase',
      mongodb_removed: true,
      whatsapp: 'connected',
      ai: 'gemini',
      version: '2.0.0',
      migration_completed: true,
      timestamp: new Date().toISOString()
    });
  });

  console.log('✅ Rotas configuradas com sucesso - MongoDB removido, Supabase ativo');
}
