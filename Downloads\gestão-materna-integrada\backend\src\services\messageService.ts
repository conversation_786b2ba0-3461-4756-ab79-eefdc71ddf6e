import { supabase, Message } from '../config/supabase';

export class MessageService {
  
  // Buscar todas as mensagens
  async findAll(filters: {
    contactId?: string;
    fromMe?: boolean;
    limit?: number;
    offset?: number;
  } = {}): Promise<Message[]> {
    try {
      let query = supabase
        .from('messages')
        .select('*')
        .order('timestamp', { ascending: false });

      // Aplicar filtros
      if (filters.contactId) {
        query = query.eq('contact_id', filters.contactId);
      }

      if (filters.fromMe !== undefined) {
        query = query.eq('from_me', filters.fromMe);
      }

      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      if (filters.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);
      }

      const { data, error } = await query;

      if (error) {
        console.error('❌ Erro ao buscar mensagens:', error);
        throw new Error(`Erro ao buscar mensagens: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      console.error('❌ Erro no MessageService.findAll:', error);
      throw error;
    }
  }

  // Buscar mensagem por ID
  async findById(id: string): Promise<Message | null> {
    try {
      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Não encontrado
        }
        console.error('❌ Erro ao buscar mensagem por ID:', error);
        throw new Error(`Erro ao buscar mensagem: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error('❌ Erro no MessageService.findById:', error);
      throw error;
    }
  }

  // Buscar mensagens por contato
  async findByContactId(contactId: string, limit: number = 50): Promise<Message[]> {
    try {
      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .eq('contact_id', contactId)
        .order('timestamp', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('❌ Erro ao buscar mensagens por contato:', error);
        throw new Error(`Erro ao buscar mensagens: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      console.error('❌ Erro no MessageService.findByContactId:', error);
      throw error;
    }
  }

  // Criar nova mensagem
  async create(messageData: Omit<Message, 'id' | 'created_at' | 'updated_at'>): Promise<Message> {
    try {
      const { data, error } = await supabase
        .from('messages')
        .insert([messageData])
        .select()
        .single();

      if (error) {
        console.error('❌ Erro ao criar mensagem:', error);
        throw new Error(`Erro ao criar mensagem: ${error.message}`);
      }

      console.log('✅ Mensagem criada com sucesso');
      return data;
    } catch (error) {
      console.error('❌ Erro no MessageService.create:', error);
      throw error;
    }
  }

  // Atualizar mensagem
  async update(id: string, updates: Partial<Message>): Promise<Message | null> {
    try {
      const { data, error } = await supabase
        .from('messages')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('❌ Erro ao atualizar mensagem:', error);
        throw new Error(`Erro ao atualizar mensagem: ${error.message}`);
      }

      console.log('✅ Mensagem atualizada com sucesso');
      return data;
    } catch (error) {
      console.error('❌ Erro no MessageService.update:', error);
      throw error;
    }
  }

  // Deletar mensagem
  async delete(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('messages')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('❌ Erro ao deletar mensagem:', error);
        throw new Error(`Erro ao deletar mensagem: ${error.message}`);
      }

      console.log('✅ Mensagem deletada com sucesso');
      return true;
    } catch (error) {
      console.error('❌ Erro no MessageService.delete:', error);
      throw error;
    }
  }

  // Contar mensagens
  async count(filters: { contactId?: string; fromMe?: boolean } = {}): Promise<number> {
    try {
      let query = supabase
        .from('messages')
        .select('*', { count: 'exact', head: true });

      if (filters.contactId) {
        query = query.eq('contact_id', filters.contactId);
      }

      if (filters.fromMe !== undefined) {
        query = query.eq('from_me', filters.fromMe);
      }

      const { count, error } = await query;

      if (error) {
        console.error('❌ Erro ao contar mensagens:', error);
        throw new Error(`Erro ao contar mensagens: ${error.message}`);
      }

      return count || 0;
    } catch (error) {
      console.error('❌ Erro no MessageService.count:', error);
      throw error;
    }
  }

  // Contar mensagens de hoje
  async countToday(): Promise<number> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const { count, error } = await supabase
        .from('messages')
        .select('*', { count: 'exact', head: true })
        .gte('timestamp', today.toISOString());

      if (error) {
        console.error('❌ Erro ao contar mensagens de hoje:', error);
        throw new Error(`Erro ao contar mensagens: ${error.message}`);
      }

      return count || 0;
    } catch (error) {
      console.error('❌ Erro no MessageService.countToday:', error);
      throw error;
    }
  }

  // Buscar mensagens por período
  async findByDateRange(startDate: Date, endDate: Date): Promise<Message[]> {
    try {
      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .gte('timestamp', startDate.toISOString())
        .lte('timestamp', endDate.toISOString())
        .order('timestamp', { ascending: false });

      if (error) {
        console.error('❌ Erro ao buscar mensagens por período:', error);
        throw new Error(`Erro ao buscar mensagens: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      console.error('❌ Erro no MessageService.findByDateRange:', error);
      throw error;
    }
  }

  // Buscar última mensagem de um contato
  async findLastByContactId(contactId: string): Promise<Message | null> {
    try {
      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .eq('contact_id', contactId)
        .order('timestamp', { ascending: false })
        .limit(1)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Não encontrado
        }
        console.error('❌ Erro ao buscar última mensagem:', error);
        throw new Error(`Erro ao buscar mensagem: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error('❌ Erro no MessageService.findLastByContactId:', error);
      throw error;
    }
  }

  // Atualizar sentimento da mensagem
  async updateSentiment(id: string, sentiment: 'positive' | 'negative' | 'neutral'): Promise<void> {
    try {
      const { error } = await supabase
        .from('messages')
        .update({ sentiment })
        .eq('id', id);

      if (error) {
        console.error('❌ Erro ao atualizar sentimento:', error);
        throw new Error(`Erro ao atualizar sentimento: ${error.message}`);
      }

      console.log('✅ Sentimento atualizado com sucesso');
    } catch (error) {
      console.error('❌ Erro no MessageService.updateSentiment:', error);
      throw error;
    }
  }
}

export const messageService = new MessageService();
export default messageService;
