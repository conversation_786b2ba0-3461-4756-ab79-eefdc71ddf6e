# Configurações do Servidor
PORT=3000
NODE_ENV=development

# MongoDB Atlas (descomente quando IP estiver liberado)
MONGODB_URI=mongodb+srv://italocabral95:<EMAIL>/gestacao-materna?retryWrites=true&w=majority&appName=Cluster0
# MongoDB Local (para desenvolvimento - descomente se preferir usar local)
# MONGODB_URI=mongodb://localhost:27017/gestacao-materna
# WhatsApp Web.js
WHATSAPP_SESSION_PATH=./sessions

# Gemini AI (substitua pela sua chave API)
GEMINI_API_KEY=AIzaSyAchEIEhbmB8mrF3DUZBWU56Tz1Vb6GYCA

# Frontend URL
FRONTEND_URL=http://localhost:5173

# Supabase Configuration - Projeto: Gestantes de Rafaela
SUPABASE_URL=https://rlwkwbxbyrdjbxpnemau.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJsd2t3YnhieXJkamJ4cG5lbWF1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1OTg0ODcsImV4cCI6MjA2NTE3NDQ4N30.p3sMFDgDl9fNth_Il0a4_9sW9jiEsyYxijhXtQjLRIY
SUPABASE_SERVICE_ROLE_KEY=PRECISO_DA_SERVICE_ROLE_KEY_AQUI

# Controle de migração - ATIVADO PARA TESTE
USE_SUPABASE=true

# JWT Configuration
JWT_SECRET=rafaela_cuida_jwt_secret_2024_muito_segura_altere_em_producao
JWT_EXPIRES_IN=7d