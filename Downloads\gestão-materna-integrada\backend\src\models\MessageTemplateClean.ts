// MODELO REMOVIDO - MongoDB substituído por Supabase
// Use messageTemplateService do Supabase em vez deste modelo

// import mongoose, { Schema, Document } from 'mongoose';

export interface IMessageTemplate extends Document {
  name: string;
  category: 'routine_checkup' | 'milestone' | 'educational' | 'emotional_support' | 'reminder' | 'emergency';
  title: string;
  content: string;
  variables: string[];
  isActive: boolean;
  pregnancyStage?: 'first_trimester' | 'second_trimester' | 'third_trimester' | 'postpartum';
  gestationalWeekMin?: number;
  gestationalWeekMax?: number;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdBy: string; // ID do usuário no Supabase
  lastModifiedBy?: string; // ID do usuário no Supabase
  usageCount: number;
  lastUsed?: Date;
  createdAt: Date;
  updatedAt: Date;

  // Métodos (agora implementados no messageTemplateService)
  render(variables: Record<string, string>): string;
  incrementUsage(): Promise<void>;
  isApplicableForWeek(week: number): boolean;
}

// TODOS OS SCHEMAS E MÉTODOS MONGODB FORAM REMOVIDOS
// Use messageTemplateService do Supabase para todas as operações

// MODELO REMOVIDO - Use messageTemplateService do Supabase
export const MessageTemplate = {
  find: () => { throw new Error('MongoDB removido - Use messageTemplateService do Supabase'); },
  findById: () => { throw new Error('MongoDB removido - Use messageTemplateService do Supabase'); },
  findOne: () => { throw new Error('MongoDB removido - Use messageTemplateService do Supabase'); },
  create: () => { throw new Error('MongoDB removido - Use messageTemplateService do Supabase'); },
  countDocuments: () => { throw new Error('MongoDB removido - Use messageTemplateService do Supabase'); },
  aggregate: () => { throw new Error('MongoDB removido - Use messageTemplateService do Supabase'); },
  updateMany: () => { throw new Error('MongoDB removido - Use messageTemplateService do Supabase'); },
  deleteMany: () => { throw new Error('MongoDB removido - Use messageTemplateService do Supabase'); },
  findByIdAndUpdate: () => { throw new Error('MongoDB removido - Use messageTemplateService do Supabase'); },
  findByIdAndDelete: () => { throw new Error('MongoDB removido - Use messageTemplateService do Supabase'); }
};
