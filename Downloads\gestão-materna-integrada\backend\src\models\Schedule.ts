// MODELO REMOVIDO - MongoDB substituído por Supabase
// Use scheduleService do Supabase em vez deste modelo

// import mongoose, { Schema, Document } from 'mongoose';

export interface ISchedule {
  contact: string; // ID do contato no Supabase
  type: 'routine_checkup' | 'milestone_message' | 'educational_content' | 'emotional_support' | 'reminder' | 'follow_up';

  // Configurações de agendamento
  scheduledFor: Date;
  frequency?: 'daily' | 'weekly' | 'biweekly' | 'monthly' | 'custom';
  customInterval?: number; // em dias

  // Conteúdo da mensagem
  title: string;
  message: string;
  messageType: 'text' | 'educational' | 'motivational' | 'reminder' | 'question';

  // Condições para envio
  gestationalWeekMin?: number;
  gestationalWeekMax?: number;
  pregnancyStage?: 'first_trimester' | 'second_trimester' | 'third_trimester' | 'postpartum';
  isHighRisk?: boolean;

  // Status e controle
  status: 'pending' | 'sent' | 'failed' | 'cancelled' | 'completed';
  sentAt?: Date;
  responseReceived?: boolean;
  responseContent?: string;

  // Configurações avançadas
  priority: 'low' | 'medium' | 'high' | 'urgent';
  requiresResponse?: boolean;
  autoReschedule?: boolean;
  maxAttempts?: number;
  attempts: number;

  // Personalização
  personalized: boolean;
  variables?: Map<string, string>; // Para personalizar mensagens

  // Dados do criador
  createdBy: string; // ID do usuário no Supabase
  lastModifiedBy?: string; // ID do usuário no Supabase

  // Métodos (agora implementados no scheduleService)
  shouldSend(): boolean;
  personalize(): string;
  markAsSent(): Promise<void>;
  reschedule(days: number): Promise<void>;
}

// TODOS OS SCHEMAS E MÉTODOS MONGODB FORAM REMOVIDOS
// Use scheduleService do Supabase para todas as operações

// MODELO REMOVIDO - Use scheduleService do Supabase
export const Schedule = {
  find: () => { throw new Error('MongoDB removido - Use scheduleService do Supabase'); },
  findById: () => { throw new Error('MongoDB removido - Use scheduleService do Supabase'); },
  findOne: () => { throw new Error('MongoDB removido - Use scheduleService do Supabase'); },
  create: () => { throw new Error('MongoDB removido - Use scheduleService do Supabase'); },
  countDocuments: () => { throw new Error('MongoDB removido - Use scheduleService do Supabase'); },
  aggregate: () => { throw new Error('MongoDB removido - Use scheduleService do Supabase'); },
  updateMany: () => { throw new Error('MongoDB removido - Use scheduleService do Supabase'); },
  deleteMany: () => { throw new Error('MongoDB removido - Use scheduleService do Supabase'); },
  findByIdAndUpdate: () => { throw new Error('MongoDB removido - Use scheduleService do Supabase'); },
  findByIdAndDelete: () => { throw new Error('MongoDB removido - Use scheduleService do Supabase'); }
};
