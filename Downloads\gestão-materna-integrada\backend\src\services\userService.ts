import { supabase, User } from '../config/supabase';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

export class UserService {
  
  // Buscar todos os usuários
  async findAll(filters: {
    isActive?: boolean;
    role?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<Omit<User, 'password'>[]> {
    try {
      let query = supabase
        .from('users')
        .select('id, name, email, role, is_active, last_login, permissions, created_at, updated_at')
        .order('created_at', { ascending: false });

      // Aplicar filtros
      if (filters.isActive !== undefined) {
        query = query.eq('is_active', filters.isActive);
      }

      if (filters.role) {
        query = query.eq('role', filters.role);
      }

      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      if (filters.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);
      }

      const { data, error } = await query;

      if (error) {
        console.error('❌ Erro ao buscar usuários:', error);
        throw new Error(`Erro ao buscar usuários: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      console.error('❌ Erro no UserService.findAll:', error);
      throw error;
    }
  }

  // Buscar usuário por ID
  async findById(id: string): Promise<Omit<User, 'password'> | null> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id, name, email, role, is_active, last_login, permissions, created_at, updated_at')
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Não encontrado
        }
        console.error('❌ Erro ao buscar usuário por ID:', error);
        throw new Error(`Erro ao buscar usuário: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error('❌ Erro no UserService.findById:', error);
      throw error;
    }
  }

  // Buscar usuário por email (com senha para autenticação)
  async findByEmail(email: string): Promise<User | null> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('email', email.toLowerCase())
        .eq('is_active', true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Não encontrado
        }
        console.error('❌ Erro ao buscar usuário por email:', error);
        throw new Error(`Erro ao buscar usuário: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error('❌ Erro no UserService.findByEmail:', error);
      throw error;
    }
  }

  // Criar novo usuário
  async create(userData: Omit<User, 'id' | 'created_at' | 'updated_at'>): Promise<Omit<User, 'password'>> {
    try {
      // Hash da senha
      const salt = await bcrypt.genSalt(12);
      const hashedPassword = await bcrypt.hash(userData.password, salt);

      const { data, error } = await supabase
        .from('users')
        .insert([{
          ...userData,
          email: userData.email.toLowerCase(),
          password: hashedPassword
        }])
        .select('id, name, email, role, is_active, last_login, permissions, created_at, updated_at')
        .single();

      if (error) {
        console.error('❌ Erro ao criar usuário:', error);
        throw new Error(`Erro ao criar usuário: ${error.message}`);
      }

      console.log('✅ Usuário criado com sucesso:', data.name);
      return data;
    } catch (error) {
      console.error('❌ Erro no UserService.create:', error);
      throw error;
    }
  }

  // Atualizar usuário
  async update(id: string, updates: Partial<Omit<User, 'id' | 'created_at' | 'updated_at'>>): Promise<Omit<User, 'password'> | null> {
    try {
      let updateData = { ...updates };

      // Se está atualizando a senha, fazer hash
      if (updates.password) {
        const salt = await bcrypt.genSalt(12);
        updateData.password = await bcrypt.hash(updates.password, salt);
      }

      // Se está atualizando email, converter para lowercase
      if (updates.email) {
        updateData.email = updates.email.toLowerCase();
      }

      const { data, error } = await supabase
        .from('users')
        .update(updateData)
        .eq('id', id)
        .select('id, name, email, role, is_active, last_login, permissions, created_at, updated_at')
        .single();

      if (error) {
        console.error('❌ Erro ao atualizar usuário:', error);
        throw new Error(`Erro ao atualizar usuário: ${error.message}`);
      }

      console.log('✅ Usuário atualizado com sucesso:', data.name);
      return data;
    } catch (error) {
      console.error('❌ Erro no UserService.update:', error);
      throw error;
    }
  }

  // Deletar usuário (soft delete)
  async delete(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('users')
        .update({ is_active: false })
        .eq('id', id);

      if (error) {
        console.error('❌ Erro ao deletar usuário:', error);
        throw new Error(`Erro ao deletar usuário: ${error.message}`);
      }

      console.log('✅ Usuário desativado com sucesso:', id);
      return true;
    } catch (error) {
      console.error('❌ Erro no UserService.delete:', error);
      throw error;
    }
  }

  // Autenticar usuário
  async authenticate(email: string, password: string): Promise<{ user: Omit<User, 'password'>; token: string } | null> {
    try {
      const user = await this.findByEmail(email);
      
      if (!user) {
        return null;
      }

      // Verificar senha
      const isMatch = await bcrypt.compare(password, user.password);
      if (!isMatch) {
        return null;
      }

      // Atualizar último login
      await this.updateLastLogin(user.id!);

      // Gerar token JWT
      const token = this.generateAuthToken(user);

      // Remover senha do retorno
      const { password: _, ...userWithoutPassword } = user;

      return {
        user: userWithoutPassword,
        token
      };
    } catch (error) {
      console.error('❌ Erro no UserService.authenticate:', error);
      throw error;
    }
  }

  // Atualizar último login
  async updateLastLogin(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('users')
        .update({ last_login: new Date().toISOString() })
        .eq('id', id);

      if (error) {
        console.error('❌ Erro ao atualizar último login:', error);
        throw new Error(`Erro ao atualizar último login: ${error.message}`);
      }
    } catch (error) {
      console.error('❌ Erro no UserService.updateLastLogin:', error);
      throw error;
    }
  }

  // Gerar token JWT
  generateAuthToken(user: User): string {
    const payload = {
      id: user.id,
      email: user.email,
      role: user.role,
      permissions: user.permissions
    };

    return jwt.sign(payload, process.env.JWT_SECRET || 'fallback-secret', {
      expiresIn: process.env.JWT_EXPIRES_IN || '7d'
    });
  }

  // Verificar token JWT
  verifyToken(token: string): any {
    try {
      return jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret');
    } catch (error) {
      console.error('❌ Token inválido:', error);
      return null;
    }
  }

  // Contar usuários
  async count(filters: { isActive?: boolean; role?: string } = {}): Promise<number> {
    try {
      let query = supabase
        .from('users')
        .select('*', { count: 'exact', head: true });

      if (filters.isActive !== undefined) {
        query = query.eq('is_active', filters.isActive);
      }

      if (filters.role) {
        query = query.eq('role', filters.role);
      }

      const { count, error } = await query;

      if (error) {
        console.error('❌ Erro ao contar usuários:', error);
        throw new Error(`Erro ao contar usuários: ${error.message}`);
      }

      return count || 0;
    } catch (error) {
      console.error('❌ Erro no UserService.count:', error);
      throw error;
    }
  }
}

export const userService = new UserService();
export default userService;
