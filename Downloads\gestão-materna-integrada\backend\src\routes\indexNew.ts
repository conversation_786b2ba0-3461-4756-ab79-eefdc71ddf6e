// Arquivo de rotas limpo - apenas Supabase (MongoDB removido)
import express from 'express';
import { WhatsAppClient } from '../services/whatsapp';
import { GeminiAIService } from '../services/gemini';
import healthMonitor from '../services/healthMonitor';

// Importar rotas modulares
import authRoutes from './auth';
import contactRoutes from './contacts';
import messageRoutes from './messages';
import analyticsRoutes from './analytics';
import whatsappRoutes, { setWhatsAppClient } from './whatsapp';
import scheduleRoutes from './schedules';
import templateRoutes from './templates';
import webhookRoutes from './webhooks';
import healthRoutes from './health';

// Importar rotas Supabase (substitui MongoDB)
import { setupSupabaseRoutes } from './supabaseRoutes';

export function setupRoutes(app: express.Application, whatsappClient: WhatsAppClient, geminiService: GeminiAIService) {
  // Configurar WhatsApp client nas rotas
  setWhatsAppClient(whatsappClient);

  // =====================================================
  // ROTAS SUPABASE (SUBSTITUI MONGODB COMPLETAMENTE)
  // =====================================================
  setupSupabaseRoutes(app);

  // =====================================================
  // ROTAS COM AUTENTICAÇÃO
  // =====================================================

  // Rotas de autenticação
  app.use('/api/auth', authRoutes);

  // Rotas de contatos (se houver rotas específicas)
  app.use('/api/contacts', contactRoutes);

  // Rotas de mensagens
  app.use('/api/messages', messageRoutes);

  // Rotas de analytics
  app.use('/api/analytics', analyticsRoutes);

  // Rotas do WhatsApp
  app.use('/api/whatsapp', whatsappRoutes);

  // Rotas de agendamentos proativos
  app.use('/api/schedules', scheduleRoutes);

  // Rotas de templates de mensagens
  app.use('/api/templates', templateRoutes);

  // Rotas de webhooks
  app.use('/api/webhooks', webhookRoutes);

  // Rotas de monitoramento de saúde
  app.use('/api/health', healthRoutes);

  // =====================================================
  // ROTAS DE UTILIDADE
  // =====================================================

  // Rota de health check básico
  app.get('/api/health-simple', (req, res) => {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      database: 'Supabase',
      mongodb_removed: true,
      version: '2.0.0'
    });
  });

  // Rota para processar mensagem com IA
  app.post('/api/ai/process', async (req, res) => {
    try {
      const { message, contactId } = req.body;
      
      if (!contactId) {
        return res.status(400).json({ error: 'ID do contato é obrigatório' });
      }

      // Processar com IA usando Gemini
      const aiResponse = await geminiService.processMessage(message, contactId);
      
      res.json({
        originalMessage: message,
        aiResponse: aiResponse,
        contactId: contactId,
        timestamp: new Date().toISOString()
      });
    } catch (error: any) {
      console.error('❌ Erro ao processar mensagem com IA:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Rota para verificar status do sistema
  app.get('/api/system/status', (req, res) => {
    res.json({
      status: 'operational',
      database: 'Supabase',
      mongodb_removed: true,
      whatsapp: 'connected',
      ai: 'gemini',
      version: '2.0.0',
      migration_completed: true,
      timestamp: new Date().toISOString()
    });
  });

  // Rota para informações do sistema
  app.get('/api/system/info', (req, res) => {
    res.json({
      name: 'Sistema Rafaela Cuida',
      version: '2.0.0',
      database: 'Supabase (PostgreSQL)',
      mongodb_status: 'Removido',
      migration_date: '2025-06-11',
      features: [
        'Gestão de gestantes',
        'WhatsApp integrado',
        'IA Gemini',
        'Dashboard em tempo real',
        'Analytics avançados'
      ],
      endpoints: [
        'GET /api/contacts - Listar gestantes',
        'POST /api/contacts - Criar gestante',
        'PUT /api/contacts/:id - Atualizar gestante',
        'DELETE /api/contacts/:id - Deletar gestante',
        'GET /api/analytics/dashboard - Métricas',
        'GET /api/analytics/new-registrations - Registros por mês',
        'POST /api/ai/process - Processar com IA'
      ]
    });
  });

  console.log('✅ Rotas configuradas com sucesso');
  console.log('🗑️ MongoDB removido completamente');
  console.log('🚀 Supabase ativo como banco principal');
  console.log('📊 Sistema funcionando em modo Supabase');
}
