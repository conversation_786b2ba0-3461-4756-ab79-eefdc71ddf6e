// MODELO REMOVIDO - MongoDB substituído por Supabase
// Use userService do Supabase em vez deste modelo

// import mongoose, { Schema, Document } from 'mongoose';

export interface IUser extends Document {
  name: string;
  email: string;
  password: string;
  role: 'admin' | 'nurse' | 'doctor' | 'coordinator';
  isActive: boolean;
  lastLogin?: Date;
  permissions: string[];
  createdAt: Date;
  updatedAt: Date;

  // Métodos (agora implementados no userService)
  comparePassword(password: string): Promise<boolean>;
  generateAuthToken(): string;
  updateLastLogin(): Promise<void>;
  hasPermission(permission: string): boolean;
  activate(): Promise<void>;
  deactivate(): Promise<void>;
}

// TODOS OS SCHEMAS E MÉTODOS MONGODB FORAM REMOVIDOS
// Use userService do Supabase para todas as operações

// MODELO REMOVIDO - Use userService do Supabase
export const User = {
  find: () => { throw new Error('MongoDB removido - Use userService do Supabase'); },
  findById: () => { throw new Error('MongoDB removido - Use userService do Supabase'); },
  findOne: () => { throw new Error('MongoDB removido - Use userService do Supabase'); },
  create: () => { throw new Error('MongoDB removido - Use userService do Supabase'); },
  findByCredentials: () => { throw new Error('MongoDB removido - Use userService.authenticate do Supabase'); }
};
