// MODELO REMOVIDO - MongoDB substituído por Supabase
// Use contactService do Supabase em vez deste modelo

// import mongoose, { Schema, Document } from 'mongoose';

export interface IContact extends Document {
  name: string;
  phone: string;
  babyGender: 'male' | 'female' | 'unknown';
  isActive: boolean;
  lastInteraction: Date;
  registrationStatus: 'unregistered' | 'registered' | 'not_interested';
  evaluationMessages: number;
  interestScore: number;
  evaluationStartDate?: Date;
  createdAt: Date;
  updatedAt: Date;

  // Métodos (agora implementados no contactService)
  updateInteraction(): Promise<void>;
  incrementEvaluationMessages(): Promise<void>;
  updateInterestScore(score: number): Promise<void>;
  markAsRegistered(): Promise<void>;
  markAsNotInterested(): Promise<void>;
  deactivate(): Promise<void>;
  reactivate(): Promise<void>;
}

// TODOS OS SCHEMAS E MÉTODOS MONGODB FORAM REMOVIDOS
// Use contactService do Supabase para todas as operações

// MODELO REMOVIDO - Use contactService do Supabase
export const Contact = {
  find: () => { throw new Error('MongoDB removido - Use contactService do Supabase'); },
  findById: () => { throw new Error('MongoDB removido - Use contactService do Supabase'); },
  findOne: () => { throw new Error('MongoDB removido - Use contactService do Supabase'); },
  create: () => { throw new Error('MongoDB removido - Use contactService do Supabase'); },
  countDocuments: () => { throw new Error('MongoDB removido - Use contactService do Supabase'); },
  aggregate: () => { throw new Error('MongoDB removido - Use contactService do Supabase'); },
  updateMany: () => { throw new Error('MongoDB removido - Use contactService do Supabase'); },
  deleteMany: () => { throw new Error('MongoDB removido - Use contactService do Supabase'); },
  findByIdAndUpdate: () => { throw new Error('MongoDB removido - Use contactService do Supabase'); },
  findByIdAndDelete: () => { throw new Error('MongoDB removido - Use contactService do Supabase'); }
};
