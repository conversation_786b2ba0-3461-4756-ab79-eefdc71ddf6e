import { supabase, Contact } from '../config/supabase';

export class ContactService {
  
  // Buscar todos os contatos ativos
  async findAll(filters: {
    isActive?: boolean;
    limit?: number;
    offset?: number;
    search?: string;
  } = {}): Promise<Contact[]> {
    try {
      let query = supabase
        .from('contacts')
        .select('*')
        .order('last_interaction', { ascending: false });

      // Aplicar filtros
      if (filters.isActive !== undefined) {
        query = query.eq('is_active', filters.isActive);
      }

      if (filters.search) {
        query = query.or(`name.ilike.%${filters.search}%,phone.ilike.%${filters.search}%`);
      }

      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      if (filters.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);
      }

      const { data, error } = await query;

      if (error) {
        console.error('❌ Erro ao buscar contatos:', error);
        throw new Error(`Erro ao buscar contatos: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      console.error('❌ Erro no ContactService.findAll:', error);
      throw error;
    }
  }

  // Buscar contato por ID
  async findById(id: string): Promise<Contact | null> {
    try {
      const { data, error } = await supabase
        .from('contacts')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Não encontrado
        }
        console.error('❌ Erro ao buscar contato por ID:', error);
        throw new Error(`Erro ao buscar contato: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error('❌ Erro no ContactService.findById:', error);
      throw error;
    }
  }

  // Buscar contato por telefone
  async findByPhone(phone: string): Promise<Contact | null> {
    try {
      const { data, error } = await supabase
        .from('contacts')
        .select('*')
        .eq('phone', phone)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Não encontrado
        }
        console.error('❌ Erro ao buscar contato por telefone:', error);
        throw new Error(`Erro ao buscar contato: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error('❌ Erro no ContactService.findByPhone:', error);
      throw error;
    }
  }

  // Criar novo contato
  async create(contactData: Omit<Contact, 'id' | 'created_at' | 'updated_at'>): Promise<Contact> {
    try {
      const { data, error } = await supabase
        .from('contacts')
        .insert([contactData])
        .select()
        .single();

      if (error) {
        console.error('❌ Erro ao criar contato:', error);
        throw new Error(`Erro ao criar contato: ${error.message}`);
      }

      console.log('✅ Contato criado com sucesso:', data.name);
      return data;
    } catch (error) {
      console.error('❌ Erro no ContactService.create:', error);
      throw error;
    }
  }

  // Atualizar contato
  async update(id: string, updates: Partial<Contact>): Promise<Contact | null> {
    try {
      const { data, error } = await supabase
        .from('contacts')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('❌ Erro ao atualizar contato:', error);
        throw new Error(`Erro ao atualizar contato: ${error.message}`);
      }

      console.log('✅ Contato atualizado com sucesso:', data.name);
      return data;
    } catch (error) {
      console.error('❌ Erro no ContactService.update:', error);
      throw error;
    }
  }

  // Atualizar última interação
  async updateLastInteraction(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('contacts')
        .update({ last_interaction: new Date().toISOString() })
        .eq('id', id);

      if (error) {
        console.error('❌ Erro ao atualizar última interação:', error);
        throw new Error(`Erro ao atualizar última interação: ${error.message}`);
      }

      console.log('✅ Última interação atualizada para contato:', id);
    } catch (error) {
      console.error('❌ Erro no ContactService.updateLastInteraction:', error);
      throw error;
    }
  }

  // Deletar contato (soft delete)
  async delete(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('contacts')
        .update({ is_active: false })
        .eq('id', id);

      if (error) {
        console.error('❌ Erro ao deletar contato:', error);
        throw new Error(`Erro ao deletar contato: ${error.message}`);
      }

      console.log('✅ Contato desativado com sucesso:', id);
      return true;
    } catch (error) {
      console.error('❌ Erro no ContactService.delete:', error);
      throw error;
    }
  }

  // Contar contatos
  async count(filters: { isActive?: boolean } = {}): Promise<number> {
    try {
      let query = supabase
        .from('contacts')
        .select('*', { count: 'exact', head: true });

      if (filters.isActive !== undefined) {
        query = query.eq('is_active', filters.isActive);
      }

      const { count, error } = await query;

      if (error) {
        console.error('❌ Erro ao contar contatos:', error);
        throw new Error(`Erro ao contar contatos: ${error.message}`);
      }

      return count || 0;
    } catch (error) {
      console.error('❌ Erro no ContactService.count:', error);
      throw error;
    }
  }

  // Buscar contatos por gênero do bebê
  async countByBabyGender(): Promise<{ male: number; female: number; unknown: number }> {
    try {
      const [maleResult, femaleResult, unknownResult] = await Promise.all([
        supabase.from('contacts').select('*', { count: 'exact', head: true }).eq('is_active', true).eq('baby_gender', 'male'),
        supabase.from('contacts').select('*', { count: 'exact', head: true }).eq('is_active', true).eq('baby_gender', 'female'),
        supabase.from('contacts').select('*', { count: 'exact', head: true }).eq('is_active', true).eq('baby_gender', 'unknown')
      ]);

      return {
        male: maleResult.count || 0,
        female: femaleResult.count || 0,
        unknown: unknownResult.count || 0
      };
    } catch (error) {
      console.error('❌ Erro no ContactService.countByBabyGender:', error);
      throw error;
    }
  }

  // Buscar registros por mês (últimos 6 meses)
  async getRegistrationsByMonth(): Promise<Array<{ name: string; value: number }>> {
    try {
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

      const { data, error } = await supabase
        .from('contacts')
        .select('created_at')
        .eq('is_active', true)
        .gte('created_at', sixMonthsAgo.toISOString());

      if (error) {
        console.error('❌ Erro ao buscar registros por mês:', error);
        throw new Error(`Erro ao buscar registros: ${error.message}`);
      }

      // Agrupar por mês
      const monthCounts: { [key: string]: number } = {};
      const monthNames = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];

      data?.forEach(contact => {
        const date = new Date(contact.created_at);
        const monthKey = `${monthNames[date.getMonth()]}/${date.getFullYear()}`;
        monthCounts[monthKey] = (monthCounts[monthKey] || 0) + 1;
      });

      // Converter para formato do gráfico
      return Object.entries(monthCounts).map(([name, value]) => ({ name, value }));
    } catch (error) {
      console.error('❌ Erro no ContactService.getRegistrationsByMonth:', error);
      throw error;
    }
  }
}

export const contactService = new ContactService();
