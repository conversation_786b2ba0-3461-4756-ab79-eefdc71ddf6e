import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  throw new Error('Supabase URL e Key são obrigatórios. Verifique as variáveis de ambiente SUPABASE_URL e SUPABASE_ANON_KEY');
}

// Cliente Supabase
export const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: false, // Para backend, não precisamos persistir sessão
  },
  db: {
    schema: 'public'
  }
});

// Tipos para as tabelas
export interface Contact {
  id?: string;
  name: string;
  phone: string;
  baby_gender?: 'male' | 'female' | 'unknown';
  is_active: boolean;
  last_interaction: string;
  created_by?: string;
  registration_status: 'unregistered' | 'evaluating' | 'interested' | 'registered' | 'not_interested';
  evaluation_start_date?: string;
  evaluation_messages: number;
  interest_score: number;
  created_at?: string;
  updated_at?: string;
}

export interface Message {
  id?: string;
  contact_id: string;
  content: string;
  timestamp: string;
  type: 'text' | 'chat' | 'image' | 'audio' | 'video' | 'document' | 'location' | 'contact_card' | 'sticker';
  from_me: boolean;
  message_id?: string;
  status?: 'sent' | 'delivered' | 'read' | 'failed';
  whatsapp_data?: any;
  media_data?: any;
  sentiment?: 'positive' | 'negative' | 'neutral';
  category?: string;
  created_at?: string;
  updated_at?: string;
}

export interface User {
  id?: string;
  name: string;
  email: string;
  password: string;
  role: 'admin' | 'nurse' | 'doctor' | 'coordinator';
  is_active: boolean;
  last_login?: string;
  permissions: string[];
  created_at?: string;
  updated_at?: string;
}

export interface MessageTemplate {
  id?: string;
  name: string;
  category: 'routine_checkup' | 'milestone' | 'educational' | 'emotional_support' | 'reminder' | 'emergency_follow_up';
  title: string;
  content: string;
  variables: string[];
  gestational_week_min?: number;
  gestational_week_max?: number;
  pregnancy_stage?: 'first_trimester' | 'second_trimester' | 'third_trimester' | 'postpartum';
  is_high_risk?: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  requires_response: boolean;
  follow_up_days?: number;
  is_active: boolean;
  created_by?: string;
  created_at?: string;
  updated_at?: string;
}

// Função para testar conexão
export const testSupabaseConnection = async (): Promise<boolean> => {
  try {
    const { data, error } = await supabase.from('contacts').select('count').limit(1);
    
    if (error) {
      console.error('❌ Erro ao conectar com Supabase:', error.message);
      return false;
    }
    
    console.log('✅ Conexão com Supabase estabelecida com sucesso');
    return true;
  } catch (error) {
    console.error('❌ Erro ao testar conexão Supabase:', error);
    return false;
  }
};

export default supabase;
