// Script para migrar dados do MongoDB para Supabase
const { createClient } = require('@supabase/supabase-js');
const mongoose = require('mongoose');
require('dotenv').config();

// Configuração Supabase
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Configuração MongoDB
const mongoUri = process.env.MONGODB_URI;

// Schemas MongoDB (simplificados)
const contactSchema = new mongoose.Schema({
  name: String,
  phone: String,
  babyGender: String,
  isActive: Boolean,
  lastInteraction: Date,
  registrationStatus: String,
  evaluationStartDate: Date,
  evaluationMessages: Number,
  interestScore: Number,
  createdAt: Date,
  updatedAt: Date
});

const messageSchema = new mongoose.Schema({
  contact: mongoose.Schema.Types.ObjectId,
  content: String,
  fromMe: Boolean,
  timestamp: Date,
  type: String,
  messageId: String,
  status: String,
  whatsappData: Object,
  mediaData: Object,
  sentiment: String,
  category: String,
  createdAt: Date,
  updatedAt: Date
});

const Contact = mongoose.model('Contact', contactSchema);
const Message = mongoose.model('Message', messageSchema);

async function migrateContacts() {
  try {
    console.log('📋 Iniciando migração de contatos...');
    
    // Buscar todos os contatos do MongoDB
    const mongoContacts = await Contact.find({}).lean();
    console.log(`📊 Encontrados ${mongoContacts.length} contatos no MongoDB`);
    
    if (mongoContacts.length === 0) {
      console.log('⚠️ Nenhum contato encontrado no MongoDB');
      return { success: true, migrated: 0 };
    }
    
    let migratedCount = 0;
    let errorCount = 0;
    
    for (const mongoContact of mongoContacts) {
      try {
        // Mapear dados do MongoDB para Supabase
        const supabaseContact = {
          name: mongoContact.name,
          phone: mongoContact.phone,
          baby_gender: mongoContact.babyGender || 'unknown',
          is_active: mongoContact.isActive !== undefined ? mongoContact.isActive : true,
          last_interaction: mongoContact.lastInteraction || new Date().toISOString(),
          registration_status: mongoContact.registrationStatus || 'unregistered',
          evaluation_start_date: mongoContact.evaluationStartDate,
          evaluation_messages: mongoContact.evaluationMessages || 0,
          interest_score: mongoContact.interestScore || 0,
          created_at: mongoContact.createdAt || new Date().toISOString(),
          updated_at: mongoContact.updatedAt || new Date().toISOString()
        };
        
        // Inserir no Supabase (ou atualizar se já existir)
        const { data, error } = await supabase
          .from('contacts')
          .upsert(supabaseContact, { 
            onConflict: 'phone',
            ignoreDuplicates: false 
          })
          .select();
        
        if (error) {
          console.error(`❌ Erro ao migrar contato ${mongoContact.name}:`, error.message);
          errorCount++;
        } else {
          console.log(`✅ Contato migrado: ${mongoContact.name}`);
          migratedCount++;
        }
        
      } catch (contactError) {
        console.error(`❌ Erro no contato ${mongoContact.name}:`, contactError.message);
        errorCount++;
      }
    }
    
    console.log(`📊 Migração de contatos concluída:`);
    console.log(`   ✅ Migrados: ${migratedCount}`);
    console.log(`   ❌ Erros: ${errorCount}`);
    
    return { success: true, migrated: migratedCount, errors: errorCount };
    
  } catch (error) {
    console.error('❌ Erro na migração de contatos:', error.message);
    return { success: false, error: error.message };
  }
}

async function migrateMessages() {
  try {
    console.log('💬 Iniciando migração de mensagens...');
    
    // Buscar todas as mensagens do MongoDB
    const mongoMessages = await Message.find({}).populate('contact').lean();
    console.log(`📊 Encontradas ${mongoMessages.length} mensagens no MongoDB`);
    
    if (mongoMessages.length === 0) {
      console.log('⚠️ Nenhuma mensagem encontrada no MongoDB');
      return { success: true, migrated: 0 };
    }
    
    // Buscar mapeamento de contatos (MongoDB ID -> Supabase ID)
    const { data: supabaseContacts, error: contactsError } = await supabase
      .from('contacts')
      .select('id, phone');
    
    if (contactsError) {
      console.error('❌ Erro ao buscar contatos do Supabase:', contactsError.message);
      return { success: false, error: contactsError.message };
    }
    
    // Criar mapeamento phone -> supabase_id
    const phoneToIdMap = {};
    supabaseContacts.forEach(contact => {
      phoneToIdMap[contact.phone] = contact.id;
    });
    
    let migratedCount = 0;
    let errorCount = 0;
    
    for (const mongoMessage of mongoMessages) {
      try {
        // Encontrar o ID do contato no Supabase
        const contactPhone = mongoMessage.contact?.phone;
        const supabaseContactId = phoneToIdMap[contactPhone];
        
        if (!supabaseContactId) {
          console.warn(`⚠️ Contato não encontrado no Supabase para mensagem: ${contactPhone}`);
          errorCount++;
          continue;
        }
        
        // Mapear dados da mensagem
        const supabaseMessage = {
          contact_id: supabaseContactId,
          content: mongoMessage.content,
          timestamp: mongoMessage.timestamp || new Date().toISOString(),
          type: mongoMessage.type || 'text',
          from_me: mongoMessage.fromMe,
          message_id: mongoMessage.messageId,
          status: mongoMessage.status || 'sent',
          whatsapp_data: mongoMessage.whatsappData,
          media_data: mongoMessage.mediaData,
          sentiment: mongoMessage.sentiment,
          category: mongoMessage.category,
          created_at: mongoMessage.createdAt || new Date().toISOString(),
          updated_at: mongoMessage.updatedAt || new Date().toISOString()
        };
        
        // Inserir no Supabase
        const { data, error } = await supabase
          .from('messages')
          .insert(supabaseMessage)
          .select();
        
        if (error) {
          console.error(`❌ Erro ao migrar mensagem:`, error.message);
          errorCount++;
        } else {
          migratedCount++;
          if (migratedCount % 10 === 0) {
            console.log(`📊 Migradas ${migratedCount} mensagens...`);
          }
        }
        
      } catch (messageError) {
        console.error(`❌ Erro na mensagem:`, messageError.message);
        errorCount++;
      }
    }
    
    console.log(`📊 Migração de mensagens concluída:`);
    console.log(`   ✅ Migradas: ${migratedCount}`);
    console.log(`   ❌ Erros: ${errorCount}`);
    
    return { success: true, migrated: migratedCount, errors: errorCount };
    
  } catch (error) {
    console.error('❌ Erro na migração de mensagens:', error.message);
    return { success: false, error: error.message };
  }
}

async function runMigration() {
  try {
    console.log('🚀 Iniciando migração MongoDB → Supabase');
    console.log('=====================================');
    
    // Conectar ao MongoDB
    console.log('🔌 Conectando ao MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('✅ Conectado ao MongoDB');
    
    // Testar conexão Supabase
    console.log('🔌 Testando conexão Supabase...');
    const { data, error } = await supabase.from('contacts').select('count').limit(1);
    if (error) {
      throw new Error(`Erro Supabase: ${error.message}`);
    }
    console.log('✅ Conectado ao Supabase');
    
    // Migrar contatos
    const contactsResult = await migrateContacts();
    if (!contactsResult.success) {
      throw new Error(`Erro na migração de contatos: ${contactsResult.error}`);
    }
    
    // Migrar mensagens
    const messagesResult = await migrateMessages();
    if (!messagesResult.success) {
      throw new Error(`Erro na migração de mensagens: ${messagesResult.error}`);
    }
    
    console.log('\n🎉 MIGRAÇÃO CONCLUÍDA COM SUCESSO!');
    console.log('=====================================');
    console.log(`📊 Resumo:`);
    console.log(`   👥 Contatos migrados: ${contactsResult.migrated}`);
    console.log(`   💬 Mensagens migradas: ${messagesResult.migrated}`);
    console.log(`   ❌ Total de erros: ${(contactsResult.errors || 0) + (messagesResult.errors || 0)}`);
    
    // Fechar conexão MongoDB
    await mongoose.disconnect();
    console.log('🔌 Desconectado do MongoDB');
    
    process.exit(0);
    
  } catch (error) {
    console.error('💥 Erro fatal na migração:', error.message);
    process.exit(1);
  }
}

// Executar migração
runMigration();
