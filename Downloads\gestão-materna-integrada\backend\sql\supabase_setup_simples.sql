-- =====================================================
-- SCRIPT SIMPLIFICADO PARA SUPABASE
-- Sistema Rafaela Cuida - Gestantes de Rafaela
-- =====================================================

-- <PERSON><PERSON><PERSON><PERSON> necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- TABELA: users (Usuários do sistema)
-- =====================================================
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL DEFAULT 'nurse',
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMPTZ,
    permissions TEXT[] DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- TABELA: contacts (Gestantes)
-- =====================================================
CREATE TABLE IF NOT EXISTS contacts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20) UNIQUE NOT NULL,
    baby_gender VARCHAR(10) DEFAULT 'unknown',
    is_active BOOLEAN DEFAULT true,
    last_interaction TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    registration_status VARCHAR(20) DEFAULT 'unregistered',
    evaluation_start_date TIMESTAMPTZ,
    evaluation_messages INTEGER DEFAULT 0,
    interest_score INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- TABELA: messages (Mensagens)
-- =====================================================
CREATE TABLE IF NOT EXISTS messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    contact_id UUID NOT NULL REFERENCES contacts(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    type VARCHAR(20) DEFAULT 'text',
    from_me BOOLEAN NOT NULL,
    message_id VARCHAR(255) UNIQUE,
    status VARCHAR(20) DEFAULT 'sent',
    whatsapp_data JSONB,
    media_data JSONB,
    sentiment VARCHAR(10),
    category VARCHAR(50),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- TABELA: message_templates (Templates de mensagens)
-- =====================================================
CREATE TABLE IF NOT EXISTS message_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) UNIQUE NOT NULL,
    category VARCHAR(30) NOT NULL,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    variables TEXT[] DEFAULT '{}',
    gestational_week_min INTEGER,
    gestational_week_max INTEGER,
    pregnancy_stage VARCHAR(20),
    is_high_risk BOOLEAN,
    priority VARCHAR(10) DEFAULT 'medium',
    requires_response BOOLEAN DEFAULT false,
    follow_up_days INTEGER,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- ÍNDICES BÁSICOS
-- =====================================================
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_contacts_phone ON contacts(phone);
CREATE INDEX IF NOT EXISTS idx_contacts_is_active ON contacts(is_active);
CREATE INDEX IF NOT EXISTS idx_messages_contact_id ON messages(contact_id);
CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON messages(timestamp DESC);

-- =====================================================
-- FUNÇÃO PARA UPDATED_AT
-- =====================================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- =====================================================
-- TRIGGERS
-- =====================================================
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_contacts_updated_at BEFORE UPDATE ON contacts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_messages_updated_at BEFORE UPDATE ON messages FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_message_templates_updated_at BEFORE UPDATE ON message_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- USUÁRIO ADMIN PADRÃO
-- =====================================================
INSERT INTO users (name, email, password, role, permissions) 
VALUES (
    'Administrador Rafaela',
    '<EMAIL>',
    '$2a$12$LQv3c1yqBw2fyuPifeS2aOEFc6LhAVqDgUBn9n3A.H2uab33vgibS',
    'admin',
    ARRAY['read:all', 'write:all', 'delete:all', 'manage:users', 'manage:system']
) ON CONFLICT (email) DO NOTHING;

-- =====================================================
-- DADOS DE TESTE (GESTANTES EXEMPLO)
-- =====================================================
INSERT INTO contacts (name, phone, baby_gender, registration_status, interest_score) VALUES
('Maria Silva', '+5511999999001', 'female', 'registered', 85),
('Ana Santos', '+5511999999002', 'male', 'registered', 92),
('Carla Oliveira', '+5511999999003', 'unknown', 'evaluating', 67),
('Fernanda Costa', '+5511999999004', 'female', 'interested', 78),
('Juliana Lima', '+5511999999005', 'male', 'registered', 88)
ON CONFLICT (phone) DO NOTHING;

-- =====================================================
-- MENSAGENS DE EXEMPLO
-- =====================================================
INSERT INTO messages (contact_id, content, from_me, type) 
SELECT 
    c.id,
    'Olá! Como você está se sentindo hoje?',
    true,
    'text'
FROM contacts c
WHERE c.name = 'Maria Silva'
ON CONFLICT (message_id) DO NOTHING;

INSERT INTO messages (contact_id, content, from_me, type) 
SELECT 
    c.id,
    'Estou bem, obrigada! Sentindo o bebê mexer bastante.',
    false,
    'text'
FROM contacts c
WHERE c.name = 'Maria Silva'
ON CONFLICT (message_id) DO NOTHING;
