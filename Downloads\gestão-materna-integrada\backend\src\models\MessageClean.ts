// MODELO REMOVIDO - MongoDB substituído por Supabase
// Use messageService do Supabase em vez deste modelo

// import mongoose, { Schema, Document } from 'mongoose';

export interface IMessage extends Document {
  contact: string; // ID do contato no Supabase
  content: string;
  timestamp: Date;
  type: 'text' | 'chat' | 'image' | 'audio' | 'video' | 'document' | 'location' | 'contact_card' | 'sticker';
  fromMe: boolean;
  messageId?: string;
  status: 'sent' | 'delivered' | 'read' | 'failed';

  // Dados do WhatsApp
  whatsappData?: {
    id?: string;
    chatId?: string;
    quotedMsgId?: string;
    isForwarded?: boolean;
    forwardingScore?: number;
  };

  // Metadados de mídia
  mediaData?: {
    filename?: string;
    mimetype?: string;
    filesize?: number;
    url?: string;
    caption?: string;
    duration?: number;
    width?: number;
    height?: number;
  };

  // Análise de sentimento
  sentiment?: {
    type: 'positive' | 'negative' | 'neutral' | 'urgent';
    score: number;
    confidence: number;
    keywords: string[];
  };

  // Necessidades e sugestões
  needs?: string[];
  suggestions?: string[];
  priority: 'low' | 'medium' | 'high' | 'urgent';

  // Categorização
  category?: 'medical' | 'emotional' | 'administrative' | 'emergency' | 'routine';
  tags?: string[];

  // Resposta automática
  autoResponse?: {
    triggered: boolean;
    responseId: string;
    responseText: string;
    timestamp: Date;
  };

  // Processamento
  processedBy?: string; // ID do usuário no Supabase
  processedAt?: Date;
  notes?: string;

  // Métodos (agora implementados no messageService)
  analyzeContent(): Promise<void>;
  generateSuggestions(): Promise<string[]>;
  markAsProcessed(userId: string, notes?: string): Promise<void>;
}

// TODOS OS SCHEMAS E MÉTODOS MONGODB FORAM REMOVIDOS
// Use messageService do Supabase para todas as operações

// MODELO REMOVIDO - Use messageService do Supabase
export const Message = {
  find: () => { throw new Error('MongoDB removido - Use messageService do Supabase'); },
  findById: () => { throw new Error('MongoDB removido - Use messageService do Supabase'); },
  findOne: () => { throw new Error('MongoDB removido - Use messageService do Supabase'); },
  create: () => { throw new Error('MongoDB removido - Use messageService do Supabase'); },
  countDocuments: () => { throw new Error('MongoDB removido - Use messageService do Supabase'); },
  aggregate: () => { throw new Error('MongoDB removido - Use messageService do Supabase'); }
};
